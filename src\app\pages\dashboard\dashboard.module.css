@font-face {
  font-family: 'Poppins';
  font-weight: 300;
  src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

/* Modern Dashboard Container */
.dashboardContainer {
  width: 100%;
  padding: 20px;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: transparent;
  min-height: 100vh;
}

/* Header Section */
.dashboardHeader {
  margin-bottom: 32px;
  position: relative;
  overflow: hidden;
  display: none;
}

@media (max-width: 576px) {
  .dashboardHeader {
    display: block;
    margin-bottom: 20px;
    padding: 0 16px;
  }
}

.headerContent {
  margin-bottom: 24px;
  position: relative;
  z-index: 2;
}

.pageTitle {
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #1E293B 0%, #334155 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;
}

.pageSubtitle {
  font-size: 14px;
  color: #64748B;
  margin: 0;
  font-weight: 400;
}

/* Stats Grid */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.statCard {
  background: #FFFFFF;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.statCard::before {
  content: ' ';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3B82F6, #10B981, #F59E0B, #EF4444);
  opacity: 1;
}

.statIcon {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  font-size: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #F8FAFC, #E2E8F0);
  border-radius: 12px;
}

.statLabel {
  font-size: 14px;
  color: #64748B;
  margin-bottom: 8px;
  font-weight: 500;
}

.statValue {
  font-size: 26px;
  font-weight: 700;
  background: linear-gradient(135deg, #1E293B 0%, #334155 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.5px;
  margin-bottom: 4px;
}

.statItemSubtext {
  font-size: 12px;
  color: #64748B;
  font-weight: 400;
  opacity: 0.8;
}

/* Overview and Graph Container */
.overviewAndGraphContainer {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 24px;
}

/* Transaction Overview Section */
.transactionOverview {
  background: #FFFFFF;
  border-radius: 24px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.transactionOverview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3B82F6, #10B981, #F59E0B, #EF4444, #8B5CF6);
  background-size: 200% 100%;
  animation: gradientFlow 3s ease-in-out infinite;
}

/* Graph Section */
.graphSection {
  background: #FFFFFF;
  border-radius: 20px;
  padding: 28px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  position: relative;
}

.sectionTitle {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #1E293B 0%, #334155 50%, #475569 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  letter-spacing: -0.5px;
}

.sectionTitle::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #3B82F6, #10B981);
  border-radius: 2px;
}

.periodText {
  font-size: 16px;
  font-weight: 600;
  color: #64748B;
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
  padding: 8px 16px;
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.periodText::before {
  content: '📅';
  font-size: 14px;
}

.periodText::after {
  content: '';
  position: absolute;
  top: 6px;
  right: 6px;
  width: 6px;
  height: 6px;
  background: linear-gradient(135deg, #10B981, #059669);
  border-radius: 50%;
  opacity: 0.7;
  animation: pulse 2s infinite;
}

.statsContainer {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin: 32px 0;
  padding: 0;
  background: transparent;
  border-radius: 0;
  border: none;
}

.statItem {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 24px;
  background: linear-gradient(135deg, #FFFFFF 0%, #F8FAFC 100%);
  border-radius: 20px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.statItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3B82F6, #10B981, #F59E0B, #EF4444, #8B5CF6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.statItem:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.statItem:hover::before {
  opacity: 1;
}

.statItem:nth-child(1)::before {
  background: linear-gradient(90deg, #3B82F6, #1D4ED8);
}

.statItem:nth-child(2)::before {
  background: linear-gradient(90deg, #10B981, #059669);
}

.statItem:nth-child(3)::before {
  background: linear-gradient(90deg, #F59E0B, #D97706);
}

.statItem:nth-child(4)::before {
  background: linear-gradient(90deg, #EF4444, #DC2626);
}

.statItem:nth-child(5)::before {
  background: linear-gradient(90deg, #8B5CF6, #7C3AED);
}

.statItem:nth-child(6)::before {
  background: linear-gradient(90deg, #06B6D4, #0891B2);
}

@media (max-width: 768px) {
  .statItem {
    padding: 20px 16px;
    margin: 8px 0;
  }
}

.statItemLabel {
  font-size: 14px;
  color: #64748B;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.statItemValue {
  font-size: 28px;
  font-weight: 800;
  background: linear-gradient(135deg, #1E293B 0%, #334155 50%, #475569 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -1px;
  line-height: 1.1;
}

.statItemSubtext {
  font-size: 13px;
  color: #64748B;
  font-weight: 500;
  opacity: 0.9;
  margin-top: 4px;
}

/* Wallet Balance Section */
.walletSection {
  background: linear-gradient(135deg, #10B981 0%, #059669 20%, #047857 100%);
  border-radius: 24px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  @media screen and (max-width: 576px) {
   box-shadow: none;
   border: none;
   backdrop-filter: none;
   padding: 10px; 
  }
}

.walletHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.walletTitle {
  font-size: 20px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.manageButton {
  padding: 10px 18px;
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  color: white;
  border-radius: 12px;
  border: none;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;

  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.walletCard {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  padding: 28px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.walletInfo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.walletIcon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #26D0CE 0%, #1A2980 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(38, 208, 206, 0.3);
  position: relative;
}

.walletIcon::before {
  content: '';
  position: absolute;
  inset: 2px;
  border-radius: 50%;
  background: #FFFFFF;
  z-index: 1;
}

.walletIcon svg,
.walletIcon img {
  position: relative;
  z-index: 2;
  width: 28px;
  height: 28px;
}

.walletDetails {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.walletCurrency {
  font-size: 18px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.walletType {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.walletBalance {
  font-size: 24px;
  font-weight: 800;
  color: #FFFFFF;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.walletActions {
  display: flex;
  gap: 16px;
  margin-top: 20px;
}

.actionButton {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #3B82F6;
  background: linear-gradient(135deg, #EFF6FF 0%, #DBEAFE 100%);
  cursor: pointer;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Currency Pairs Section */
.currencyPairsSection {
  background: #FFFFFF;
  border-radius: 20px;
  padding: 28px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.pairsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.pairCard {
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.pairHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.currencyIcon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.currencyName {
  font-size: 18px;
  font-weight: 600;
  background: linear-gradient(135deg, #1E293B 0%, #334155 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.pairStats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #64748B;
  font-size: 14px;
}

/* Referral Section */
.referralSection {
  background: #FFFFFF;
  border-radius: 20px;
  padding: 28px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.referralSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #F59E0B, #EF4444, #8B5CF6);
}

.referralContent {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.referralContent p {
  color: #64748B;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.referralCode {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #F8FAFC;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #E2E8F0;
}

.codeText {
  flex: 1;
  font-family: monospace;
  font-size: 14px;
  color: #1E293B;
  letter-spacing: 0.5px;
}

.copyButton {
  padding: 10px 18px;
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
  color: white;
  border-radius: 12px;
  border: none;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;

  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.referralSteps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

.stepItem {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  text-align: center;
}

.stepNumber {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #EFF6FF;
  color: #3B82F6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.stepTitle {
  font-size: 14px;
  font-weight: 500;
  color: #1E293B;
}

.stepDescription {
  font-size: 12px;
  color: #64748B;
}

/* Loading States */
.loadingSpinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(16, 185, 129, 0.2);
  border-top-color: #10B981;
  border-radius: 50%;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .dashboardContainer {
    padding: 16px 0;
    margin: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    box-sizing: border-box;
    overflow-x: hidden;
    position: relative;
    left: 0;
    right: 0;
  }

  .statsGrid {
    grid-template-columns: 1fr;
    width: 100%;
    max-width: 100%;
    margin: 0;
    gap: 16px;
    display: grid;
    justify-items: center;
    align-items: center;
    padding: 0 16px;
    box-sizing: border-box;
  }

  .statCard {
    width: 100%;
    max-width: 100%;
    padding: 20px 16px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-sizing: border-box;
  }

  .statIcon {
    margin: 0 auto 16px;
  }

  .statLabel {
    text-align: center;
  }

  .statValue {
    text-align: center;
  }

  .statsContainer {
    grid-template-columns: 1fr;
    width: 100%;
    max-width: 100%;
    margin: 32px 0;
    padding: 24px 16px;
    box-sizing: border-box;
  }

  /* Overview and Graph Container */
  .overviewAndGraphContainer {
    gap: 16px;
    padding: 0 16px;
  }

  /* Transaction Overview */
  .transactionOverview {
    width: 100%;
    max-width: 100%;
    margin: 0 0 15px 0;
    text-align: center;
    box-sizing: border-box;
    padding: 20px 16px;
  }

  /* Graph Section */
  .graphSection {
    width: 100%;
    max-width: 100%;
    margin: 0;
    text-align: center;
    box-sizing: border-box;
    padding: 20px 16px;
  }

  /* Wallet Section */
  .walletSection {
    width: 100%;
    max-width: 100%;
    margin: 0;
    text-align: center;
    box-sizing: border-box;
    padding: 0 16px;
  }

  .walletCard {
    flex-direction: column;
    gap: 16px;
    align-items: center;
    text-align: center;
    width: 100%;
    max-width: 100%;
    margin: 0;
    box-sizing: border-box;
    @media screen and (max-width: 576px) {
      margin-bottom: 15px;
    }
  }

  /* Currency Pairs */
  .currencyPairsSection {
    width: 100%;
    max-width: 100%;
    margin: 0;
    text-align: center;
    box-sizing: border-box;
    padding: 0 16px;
    @media screen and (max-width: 576px) {
      margin: 15px 0;
      padding: 10px;
    }
  }

  .pairsGrid {
    grid-template-columns: 1fr;
    width: 100%;
    max-width: 100%;
    margin: 0;
    box-sizing: border-box;
  }

  /* Referral Section */
  .referralSection {
    width: 100%;
    max-width: 100%;
    margin: 0;
    text-align: center;
    box-sizing: border-box;
    padding: 0 16px;
  }

  .referralSteps {
    grid-template-columns: 1fr;
    width: 100%;
    max-width: 100%;
    margin: 0;
    box-sizing: border-box;
  }

  .sectionHeader {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .walletActions {
    flex-direction: column;
  }

  .referralCode {
    flex-direction: column;
    align-items: stretch;
  }

  .copyButton {
    width: 100%;
  }
}

/* Enhanced Focus States */
.periodSelect:focus,
.copyButton:focus,
.actionButton:focus,
.manageButton:focus {
  outline: 2px solid #3B82F6;
  outline-offset: 2px;
}

/* Star Orbit Animation */
@keyframes starOrbit {
  0% {
    top: 50%;
    left: -8px;
    transform: translateY(-50%);
  }
  25% {
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
  }
  50% {
    top: 50%;
    right: -8px;
    left: auto;
    transform: translateY(-50%);
  }
  75% {
    bottom: -8px;
    top: auto;
    left: 50%;
    right: auto;
    transform: translateX(-50%);
  }
  100% {
    top: 50%;
    left: -8px;
    bottom: auto;
    right: auto;
    transform: translateY(-50%);
  }
}

/* Border Glow Animation */
@keyframes borderGlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.graphContainer {
  @media screen and (max-width: 576px) {
    margin: 15px 0;
    width: 100%;
  }
}

/* Animation Keyframes */
@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Toast Customization */
.toastWrapper {
  z-index: 9999;
}

.toastWrapper :global(.Toastify__toast-container) {
  width: auto !important;
  min-width: 300px;
  max-width: 400px;
}

.toastWrapper :global(.Toastify__toast) {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 14px !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  margin-bottom: 8px !important;
}

.toastWrapper :global(.Toastify__toast-body) {
  padding: 0 !important;
  margin: 0 !important;
}

.toastWrapper :global(.Toastify__close-button) {
  opacity: 0.7 !important;
  padding: 0 !important;
  margin-left: 12px !important;
}

.toastWrapper :global(.Toastify__close-button:hover) {
  opacity: 1 !important;
}