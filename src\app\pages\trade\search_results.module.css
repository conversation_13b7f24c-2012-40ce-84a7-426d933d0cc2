@font-face {
    font-family: 'Poppins';
    font-weight: 300;
    src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

/* Header Content Styles */
.headerContent {
    margin-bottom: 24px;
    position: relative;
    z-index: 2;
}

.pageTitle {
    font-size: 22px;
    font-weight: 700;
    background: linear-gradient(135deg, #1E293B 0%, #334155 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0 0 6px 0;
    letter-spacing: -0.5px;
}

.pageSubtitle {
    font-size: 14px;
    color: #64748B;
    margin: 0;
    font-weight: 400;
}

.main {
    background: linear-gradient(45deg, #f8f9fb 0%, #f8f9fb00 100%);
    height: 103vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Poppins;

    @media screen and (max-width : 576px) {
        width: 95%;
        margin: auto;
    }
}

.rightContainerBody {
    height: 100%;
    padding: 30px;
    border-radius: 20px;
    background: #fff;

    @media screen and (max-width : 576px) {
        padding: 0px;
    }
}

.body {
    height: 100%;
    background-color: #fff;
    width: 100%;
}

.wrapper {
    display: flex;
    width: 100%;
    height: 90vh;
    padding: 30px 10px;
    margin: auto;
    justify-content: space-between;

    @media screen and (max-width : 576px) {
        width: 100%;
        margin: auto;
    }
}

.leftContainerWrapper {
    width: 20%;
    position: relative;

    @media screen and (max-width : 576px) {
        display: none;
    }
}

.leftContainer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 10px;

}

/* need to remove later */

.logoArea {
    height: 20%;
    width: 100%;
    background-color: #4153ed;
    border-radius: 15px 15px 15px 0px;
    display: flex;
    flex-direction: column;
}

.logo {
    margin-top: 5px;
}

.profileBar {
    margin-top: 25px;
}

.profileBarContainer {
    background-color: #4f535a;
    width: 80%;
    height: 35px;
    margin: auto;
    border-radius: 100px;
    background: rgba(255, 255, 255, 0.17);
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.profileImg {
    margin-top: 4px;
    margin-left: 15px;
    margin-right: 5px;
}

.profileName {
    color: #fff;
    font-family: Poppins;
    font-size: 11px;
    font-weight: 500;
}

.profileDropDown {
    margin-left: auto;
    margin-right: 10px;
}


.header {
    color: #000;
    font-family: Poppins;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;

}

.logoContainer {
    /* width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: end; */
}

.logo {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.profileBar {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50%;
}



.rightContainer {
    width: 100%;
    height: auto;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    min-height: 100vh;

    @media screen and (max-width : 576px) {
        width: 100%;
        margin: auto;
    }
}

.rightContainerWrapper {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;

    @media screen and (max-width: 576px) {
        padding: 10px;
    }
}

.topBoxWrapper {
    width: 100%;
    border-bottom: 1px dashed #DCDCDC;
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    margin-top: 0;
    padding-bottom: 20px;

    @media screen and (max-width : 576px) {
        flex-direction: column;
        margin-bottom: 20px;
        padding-bottom: 15px;
    }
}

.topLeftBox {
    width: 50%;
    margin-right: auto;
    align-items: center;

    @media screen and (max-width : 576px) {
        width: 100%;
        margin: 0px 10px;

    }
}

.headerBtn {
    margin-bottom: 10px;
    font-size: 1.1rem;
    color: #475569;
    display: flex;
    align-items: center;
    font-weight: 500;
}

.HeaderBuyBtn {
    padding: 8px 12px;
    border-radius: 6px;
    background: #E2E8F0;
    color: #1E293B;
    margin-right: 12px;
    font-weight: 600;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tradeUsername {
    font-weight: 700;
    color: #0F172A;
}

.timeSubHeader {
    display: flex;
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;

    @media screen and (max-width : 576px) {
        display: block;
        display: flex;
    }

}

.topRightBox {
    margin-left: auto;
    justify-content: space-evenly;
    align-items: center;
    @media screen and (max-width : 576px) {
        margin-left: 0;
    }
}

.rightheaderinfo {
    display: flex;
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    margin-bottom: 10px;

    @media screen and (max-width : 576px) {
        flex-direction: column;
    }
}

.rinfo {
    margin: 0px 10px;
}

.rightheaderinfo1 {
    color: #4153ED;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    margin-left: 11px;
    border-radius: 5px;
    background: #FFF;
    display: inline-flex;
    padding: 5px 10px;
    justify-content: center;
    align-items: center;
    gap: 10px;

}

.bottomBoxWrapper {
    display: flex;
    width: 100%;
    justify-content: space-between;
    gap: 20px;

    @media screen and (max-width : 576px) {
        flex-direction: column;
        gap: 15px;
    }
}

.bottomLeftBox {
    width: 68%;
    background-color: #fff;
    padding: 25px;
    border-radius: 20px;
    box-sizing: border-box;

    @media screen and (max-width : 576px) {
        width: 100%;
        padding: 15px;
        margin-bottom: 0;
    }
}


.progressBarArea {
    /* height: 30px; */
    margin-bottom: 25px;
    display: flex;
    width: 100%;
    flex-wrap: wrap;
}

.confirmOrderInfoArea {
    margin-bottom: 25px;
}

.orderConfirmHeader {
    color: #000;
    font-family: Poppins;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    margin-bottom: 16px;
}

.orderConfirmInfo {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 8px;

    @media screen and (max-width : 576px) {
        flex-direction: column;
    }

}

.orderDialogue {
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    margin-bottom: 8px;

}

.orderConfirmInfoSingle {
    margin-right: 18px;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    color: #1E293B;
}


.paymentInfoContainer {
    width: 100%;
    min-height: 150px;
    display: flex;
    margin-bottom: 20px;
}

.paymentMethodDisplay {
    width: 30%;
    height: 100%;
    background-color: #fff;
    border: 1px solid #EBEBEB;
    background: #FFF;
    padding: 0px 10px;
    border-radius: 5px;
}

.paymentMethod {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 30px;
    padding: 12px 10px;
    background: #F8F8F8;
    color: #4153ED;
    font-family: Poppins;
    font-size: 13px;
    font-style: normal;
    font-weight: 500;
    margin-top: 10px;
}

.paymentAddressDisplay {
    width: 70%;
    min-height: 150px;
    border: 1px solid #EBEBEB;
    background: #FFF;
}

.paymentAddressName {
    display: flex;
    justify-content: space-between;
    padding: 12px 20px;
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;

}

.uploadDialogue {
    color: #4153ED;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;

}

.ctaDialogue {
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
}

.activityBtnArea {
    height: 50px;
    display: flex;
    justify-content: space-between;

    @media screen and (max-width : 576px) {
        margin-bottom: 70px;
        display: grid;
    }
}

.leftBtns {
    display: flex;
    width: 50%;
    margin-right: auto;
    cursor: pointer;

    @media screen and (max-width : 576px) {
        width: 100%;
        margin-bottom: 20px;

    }
}

.notifySellerBtn {
    display: inline-flex;
    height: 40px;
    padding: 8px 20px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 5px;
    background: #FFE47B;
    margin-right: 10px;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    color: #1E293B;
    cursor: pointer;
}

.cancelBtn {
    display: inline-flex;
    height: 40px;
    padding: 8px 20px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 5px;
    background: #F2F2F2;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    color: #64748B;
    cursor: pointer;
}

.reportBtn {
    display: inline-flex;
    height: 40px;
    padding: 8px 20px;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
    /* background: #f3769b; */
    background: linear-gradient(#CB356B, #BD3F32);
    color: #fff;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    cursor: pointer;

}

.bottomRightBox {
    height: fit-content;
    width: 32%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    border-radius: 20px;
    box-sizing: border-box;

    @media screen and (max-width : 576px) {
        width: 100%;
        margin-bottom: 30px;
    }
}


/* modal */
/* .modalWrapper {
    width: 700px;
} */

.modalHeaderCont {
    display: flex;
    justify-content: center;
    align-items: center;
}

.modalHeader {
    color: #4153ED;
    font-family: Poppins;
    font-size: 22px;
    font-style: normal;
    font-weight: 500;
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;

}

.modalHeader2 {
    color: #4153ED;
    font-family: Poppins;
    font-size: 22px;
    font-style: normal;
    font-weight: 500;
    display: flex;
    width: 100%;
    justify-content: center;

}

.issueSelect {
    color: #000;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    margin-bottom: 10px;

    display: flex;

}

.issueSelect2 {
    color: #000;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    margin-bottom: 10px;
    justify-content: center;
    display: flex;

}

.optionsBox {
    display: flex;
    flex-direction: column;
}

.inputBoxes {
    margin: 5px 0px;
}

.options {
    color: #000;
    font-family: Poppins;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    margin: 15px 0px;

    /* 50% */
}

.inputTextArea input {
    display: flex;
    width: 80%;
    height: 80px;
    padding: 15px 18px 10px 18px;

    gap: 10px;
    border-radius: 5px;
}

.submitBtnWrapper {
    width: 100%;
    border-radius: 5px;
    background: #4153ED;
    height: 40px;
    margin-top: 30px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    cursor: pointer;
}

.submitBtnWrapper2 {
    width: 100%;
    border-radius: 5px;
    height: 40px;
    margin-top: 30px;
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.submitBtn {
    color: #FFF;
    text-align: center;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    background-color: transparent;
    outline: none;
    border: none;
    cursor: pointer;
}

.disputeClose {
    height: 40px;
    padding: 8px 20px;
    color: #9D9D9D;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    border-radius: 5px;
    background: #F2F2F2;
    border: none;
}

.disputeSubmit {
    height: 40px;
    padding: 8px 20px;
    border-radius: 5px;
    background: #4153ED;
    color: #FFF;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    border: none;
    cursor: pointer;
}

.finalSubmitBtn {
    width: 100%;
    height: 40px;
    border-radius: 5px;
    border: 1px solid #4153ED;
    background: #4153ED;
    color: #FFF;
    text-align: center;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    cursor: pointer;
}

.inputUpload {
    width: 100%;
    height: 40px;
    border-radius: 5px;
    background: #F9F9F9;
}

.passedTerms {
    margin-top: 3px;
    font-size: 12px;
    font-family: poppins;
    font-weight: 500;
    min-height: 150px;
    @media screen and (max-width : 576px) {
        min-height: auto;
        padding: 10px 0px;
    }
}

.tradePayDetails {
    display: flex;
    list-style-type: none;
    justify-content: flex-start;
    align-items: center;
}

.payLi {
    margin: 5px 5px;
}

.payoutDetailsCont {
    display: flex;
}


.tagArea {
    width: 100%;
    margin-bottom: 25px;
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
}


.senderTag {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    padding: 12px 16px;
    border-radius: 8px;
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    font-weight: 600;
    color: white;
    letter-spacing: 0.3px;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.senderTag::before {
    content: '👤 ';
    margin-right: 8px;
    font-size: 16px;
}

.peerTag {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #1a2980 0%, #26D0CE 100%);
    padding: 12px 16px;
    border-radius: 8px;
    font-weight: 500;
    font-family: Poppins;
    font-size: 14px;
    color: white;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.dialogueSteps {
    margin-left: 10px;
    background-color: #4153ED;
    color: #fafafa;
    padding: 5px 20px;
    font-family: poppins;
    font-weight: 700;
    border-radius: 5px;
}

/* Status Tags */
.statusTag {
    text-align: center;
    padding: 8px 16px;
    border-radius: 8px;
    margin: 10px 0px;
    width: 100%;
    font-family: Poppins;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.completedTag {
    composes: statusTag;
    background: linear-gradient(135deg, #00b09b 0%, #96c93d 100%);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.expiredTag {
    composes: statusTag;
    background: linear-gradient(135deg, #cb2d3e 0%, #ef473a 100%);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

@media screen and (max-width: 576px) {
    .main {
        width: 100%;
        margin: 0;
        padding: 56px 0 0 0; /* Account for fixed header */
        min-height: calc(100vh - 56px);
        box-sizing: border-box;
    }

    .rightContainerBody {
        padding: 16px;
        border-radius: 0;
        background: #f8fafc;
    }

    .wrapper {
        padding: 0;
        height: auto;
        min-height: calc(100vh - 56px);
    }

    .rightContainer {
        width: 100%;
        min-height: unset;
        padding: 0;
    }

    .rightContainerWrapper {
        padding: 12px;
    }

    /* Header Improvements */
    .headerContent {
        text-align: center;
        padding: 16px;
        margin-bottom: 16px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .pageTitle {
        font-size: 20px;
        margin-bottom: 8px;
    }

    .pageSubtitle {
        font-size: 13px;
        line-height: 1.4;
    }

    /* Top Box Section */
    .topBoxWrapper {
        padding: 16px;
        margin: 0 0 16px 0;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .topLeftBox {
        margin: 0 0 16px 0;
    }

    .headerBtn {
        font-size: 14px;
        margin-bottom: 12px;
    }

    .HeaderBuyBtn {
        padding: 8px 12px;
        font-size: 12px;
    }

    .timeSubHeader {
        font-size: 12px;
        gap: 8px;
        flex-wrap: wrap;
    }

    /* Bottom Box Section */
    .bottomBoxWrapper {
        margin: 0;
    }

    .bottomLeftBox {
        padding: 16px;
        margin: 0 0 16px 0;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    /* Payment Info Section */
    .paymentInfoContainer {
        flex-direction: column;
        gap: 12px;
    }

    .paymentMethodDisplay {
        width: 100%;
        margin-bottom: 12px;
    }

    .paymentAddressDisplay {
        width: 100%;
    }

    .paymentMethod {
        padding: 10px;
        font-size: 12px;
    }

    .paymentAddressName {
        padding: 10px 16px;
        font-size: 12px;
    }

    /* Activity Buttons */
    .activityBtnArea {
        flex-direction: column;
        gap: 12px;
        margin: 16px 0;
        height: auto;
    }

    .leftBtns {
        width: 100%;
        gap: 8px;
        justify-content: center;
    }

    .notifySellerBtn,
    .cancelBtn,
    .reportBtn {
        padding: 10px 16px;
        font-size: 13px;
        height: 44px; /* Better touch target */
        flex: 1;
        white-space: nowrap;
    }

    /* Order Info Section */
    .orderConfirmHeader {
        font-size: 15px;
        margin-bottom: 12px;
    }

    .orderConfirmInfo {
        gap: 8px;
    }

    .orderConfirmInfoSingle {
        margin: 0;
        font-size: 13px;
    }

    .orderDialogue {
        font-size: 12px;
        line-height: 1.4;
    }

    /* Modal Improvements */
    .modalHeader,
    .modalHeader2 {
        font-size: 18px;
        padding: 16px;
    }

    .issueSelect,
    .issueSelect2 {
        font-size: 13px;
    }

    .submitBtnWrapper,
    .submitBtnWrapper2 {
        margin-top: 20px;
    }

    .disputeClose,
    .disputeSubmit,
    .finalSubmitBtn {
        height: 44px;
        font-size: 13px;
    }

    /* Tags */
    .senderTag,
    .peerTag {
        padding: 10px 14px;
        font-size: 13px;
        margin: 8px 0;
    }

    .dialogueSteps {
        padding: 4px 12px;
        font-size: 12px;
        margin-left: 8px;
    }

    .statusTag {
        font-size: 13px;
        padding: 8px 12px;
    }
}

/* Additional improvements for very small screens */
@media screen and (max-width: 360px) {
    .rightContainerWrapper {
        padding: 8px;
    }

    .headerBtn {
        font-size: 13px;
    }

    .notifySellerBtn,
    .cancelBtn,
    .reportBtn {
        padding: 8px 12px;
        font-size: 12px;
    }

    .orderConfirmInfoSingle {
        font-size: 12px;
    }
}

/* Hover effects for desktop */
@media (hover: hover) and (pointer: fine) {
    .completedTag:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 176, 155, 0.2);
    }

    .expiredTag:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(203, 45, 62, 0.2);
    }

    .senderTag:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(65, 88, 208, 0.2);
    }

    .peerTag:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(26, 41, 128, 0.2);
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .statusTag,
    .senderTag,
    .peerTag {
        transition: none;
    }
}