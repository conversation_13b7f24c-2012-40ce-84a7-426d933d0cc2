"use client";
import { useRef, useEffect, useState } from "react";
import Image from "next/image";
import styles from "./dashboard.module.css";
import Graph from "../../components/Dashboard/Graph/page";
import Layout from "../../components/Layout/page";
import { useRouter } from "next/navigation";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { CopyToClipboard } from "react-copy-to-clipboard";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { useTimer } from "@/app/context/TimerContext";

const Dashboard = () => {
  const router = useRouter();
  const [transactionData, setTransactionData] = useState([]);
  const [dashboardData, setDashboardData] = useState({
    total_transaction_volumes: 0,
    registered_users_count: "0",
    ad_listing_count: "0",
    total_completed_trades: "0",
  });

  const [refCode, setRefCode] = useState("");
  const [copied, setCopied] = useState(false);
  const [balance, setBalance] = useState({ asset: "USDT", free: "0.00" });
  const [isLoading, setIsLoading] = useState(true);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      const res = await customFetchWithToken.get("/dashboard/");
      setDashboardData({
        total_transaction_volumes: Number(res.data.total_transaction_volumes).toFixed(2),
        registered_users_count: res.data.registered_users_count,
        ad_listing_count: res.data.ad_listing_count,
        total_completed_trades: res.data.total_completed_trades,
      });
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  const getWalletBalance = async () => {
    try {
      const res = await customFetchWithToken.get("/get-wallet-balance/");
      setBalance(res.data.data[0]);
    } catch (error) {
      console.log(error);
      toast.error(error.response.data.message);
    }
  };

  const generateRefCode = async () => {
    const res = await customFetchWithToken.post("/referral-code/");
    setRefCode(res.data.referral_link);
  };

  const handleCopy = () => {
    setCopied(true);
    toast.success("Referral link copied to clipboard!");
  };

  useEffect(() => {
    fetchDashboardData();
    generateRefCode();
    getWalletBalance();
  }, []);

  const dashboardTitle = (
    <div className={styles.headerContent}>
      <h1 className={styles.pageTitle}>Dashboard</h1>
      <p className={styles.pageSubtitle}>
        Overview of your account activity and balances •{" "}
        {new Date().toLocaleDateString("en-US", {
          weekday: "long",
          year: "numeric",
          month: "long",
          day: "numeric",
        })}
      </p>
    </div>
  );

  return (
    <Layout title={dashboardTitle}>
      <div className={styles.dashboardContainer}>
        {/* Header Section - Hidden on desktop, shown only on mobile */}
        <div className={styles.dashboardHeader}>
          <div className={styles.headerContent}>
            <h1 className={styles.pageTitle}>Dashboard</h1>
            <p className={styles.pageSubtitle}>
              Overview of your account activity and balances •{" "}
              {new Date().toLocaleDateString("en-US", {
                weekday: "long",
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </p>
          </div>
        </div>

        {/* Stats Grid */}
        <div className={styles.statsGrid}>
          <div className={styles.statCard}>
            <div className={styles.statIcon}>💰</div>
            <div className={styles.statLabel}>Total Transaction Volume</div>
            <div className={styles.statValue}>
              ${dashboardData.total_transaction_volumes}
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>👥</div>
            <div className={styles.statLabel}>Total Completed Trades</div>
            <div className={styles.statValue}>{dashboardData.total_completed_trades}</div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>📝</div>
            <div className={styles.statLabel}>Total Active Listings</div>
            <div className={styles.statValue}>
              {dashboardData.ad_listing_count}
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>👤</div>
            <div className={styles.statLabel}>Total Users</div>
            <div className={styles.statValue}>
              {dashboardData.registered_users_count}
            </div>
          </div>
        </div>

        {/* Transaction Overview */}
        <div className={styles.overviewAndGraphContainer}>
          <div className={styles.transactionOverview}>
            <div className={styles.sectionHeader}>
              <h2 className={styles.sectionTitle}>Transaction Overview</h2>
              <span className={styles.periodText}>Last 30 Days</span>
            </div>

            <div className={styles.statsContainer}>
              <div className={styles.statItem}>
                <div className={styles.statItemLabel}>📊 Total Trades</div>
                <div className={styles.statItemValue}>59</div>
                <div className={styles.statItemSubtext}>All time trades</div>
              </div>
              <div className={styles.statItem}>
                <div className={styles.statItemLabel}>✅ Completed Trades</div>
                <div className={styles.statItemValue}>20</div>
                <div className={styles.statItemSubtext}>Success rate: 33.9%</div>
              </div>
              <div className={styles.statItem}>
                <div className={styles.statItemLabel}>⏳ Pending Trades</div>
                <div className={styles.statItemValue}>10</div>
                <div className={styles.statItemSubtext}>In progress</div>
              </div>
              <div className={styles.statItem}>
                <div className={styles.statItemLabel}>❌ Cancelled Trades</div>
                <div className={styles.statItemValue}>26</div>
                <div className={styles.statItemSubtext}>Failed or cancelled</div>
              </div>
              <div className={styles.statItem}>
                <div className={styles.statItemLabel}>💰 Total Volume</div>
                <div className={styles.statItemValue}>${Number(26424.0).toLocaleString()}</div>
                <div className={styles.statItemSubtext}>All time volume</div>
              </div>
              <div className={styles.statItem}>
                <div className={styles.statItemLabel}>📈 Average Trade</div>
                <div className={styles.statItemValue}>${Number(447.86).toLocaleString()}</div>
                <div className={styles.statItemSubtext}>Per transaction</div>
              </div>
            </div>
          </div>

          {/* Graph Section */}
          <div className={styles.graphSection}>
            <div className={styles.sectionHeader}>
              <h2 className={styles.sectionTitle}>Transaction Graph</h2>
            </div>
            <div className={styles.graphContainer}>
              <Graph />
            </div>
          </div>
        </div>

        {/* Wallet Balance */}
        <div className={styles.walletSection}>
          <div className={styles.walletHeader}>
            <h2 className={styles.walletTitle}>Wallet Balance</h2>
            <button
              className={styles.manageButton}
              onClick={() => router.push("/pages/remflowFunds")}
            >
              <span>Manage Wallet</span>
              <span>→</span>
            </button>
          </div>

          <div className={styles.walletCard}>
            <div className={styles.walletInfo}>
              <div className={styles.walletIcon}>
                <Image
                  src="/assets/payments/tether.svg"
                  alt="Tether USDT"
                  width={28}
                  height={28}
                />
              </div>
              <div className={styles.walletDetails}>
                <div className={styles.walletCurrency}>{balance.asset}</div>
              </div>
            </div>
            <div className={styles.walletBalance}>
              {isLoading ? (
                <div className={styles.loadingSpinner}></div>
              ) : (
                `${Number(balance.free).toFixed(2)} ${balance.asset}`
              )}
            </div>
          </div>
        </div>

        {/* Currency Pairs */}
        <div className={styles.currencyPairsSection}>
          <div className={styles.sectionHeader}>
            <h2 className={styles.sectionTitle}>Top Currency Pairs</h2>
          </div>

          <div className={styles.pairsGrid}>
            <div className={styles.pairCard}>
              <div className={styles.pairHeader}>
                <div className={styles.currencyIcon}>GBP</div>
                <div className={styles.currencyName}>GBP/USDT</div>
              </div>
              <div className={styles.pairStats}>
                <span>198 trades</span>
                <span>$30,325</span>
              </div>
            </div>

            <div className={styles.pairCard}>
              <div className={styles.pairHeader}>
                <div className={styles.currencyIcon}>INR</div>
                <div className={styles.currencyName}>USDT/INR</div>
              </div>
              <div className={styles.pairStats}>
                <span>27 trades</span>
                <span>$30,326</span>
              </div>
            </div>
          </div>
        </div>

        {/* Referral Section */}
        <div className={styles.referralSection}>
          <div className={styles.sectionHeader}>
            <h2 className={styles.sectionTitle}>
              Invite Friends & Earn Rewards
            </h2>
          </div>

          <div className={styles.referralContent}>
            <p>
              Share your unique referral link with friends and earn rewards when
              they sign up and complete their first transaction on Remflow.
            </p>

            <div className={styles.referralCode}>
              <span className={styles.codeText}>{refCode}</span>
              <CopyToClipboard text={refCode} onCopy={handleCopy}>
                <button className={styles.copyButton}>
                  Copy Referral Link
                </button>
              </CopyToClipboard>
            </div>

            <div className={styles.referralSteps}>
              <div className={styles.stepItem}>
                <div className={styles.stepNumber}>1</div>
                <div className={styles.stepTitle}>Share Your Link</div>
                <div className={styles.stepDescription}>
                  Send your code to friends
                </div>
              </div>
              <div className={styles.stepItem}>
                <div className={styles.stepNumber}>2</div>
                <div className={styles.stepTitle}>Friends Sign Up</div>
                <div className={styles.stepDescription}>
                  They create an account with your code
                </div>
              </div>
              <div className={styles.stepItem}>
                <div className={styles.stepNumber}>3</div>
                <div className={styles.stepTitle}>Earn Rewards</div>
                <div className={styles.stepDescription}>
                  Get bonuses for each referral
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Dashboard;
